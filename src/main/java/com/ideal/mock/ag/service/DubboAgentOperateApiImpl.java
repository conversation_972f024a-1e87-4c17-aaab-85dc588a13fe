package com.ideal.mock.ag.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.ideal.agent.gateway.api.AgentOperateApi;
import com.ideal.agent.gateway.exception.SendException;
import com.ideal.agent.gateway.exception.TaskSaveException;
import com.ideal.agent.gateway.model.AgentOperateDto;
import com.ideal.agent.gateway.model.AgentOperateResultDto;
import com.ideal.agent.gateway.model.AgentSyncOperateResultDto;
import com.ideal.agent.gateway.model.AgentTaskDto;
import com.ideal.agent.gateway.model.AgentTaskInputOutputDto;
import com.ideal.agent.gateway.model.AgentTaskQueryDto;
import com.ideal.agent.gateway.model.AgentTaskResultDto;
import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import com.ideal.snowflake.util.SnowflakeIdWorker;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.scheduling.annotation.Async;

import java.util.ArrayList;
import java.util.List;


/**
 * mock模拟agent-gateway发送任务
 *
 * <AUTHOR>
 */
@DubboService(group = "${spring.profiles.active}.agent-gateway", version = "1.0.0")
@Slf4j
public class DubboAgentOperateApiImpl implements AgentOperateApi {
    private final IPublisher iPublisher;
    private final ObjectMapper objectMapper;
    public DubboAgentOperateApiImpl(IPublisher iPublisher) {
        this.iPublisher = iPublisher;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 批量任务发送接口实现方法
     * <p>
     * 本方法响应，代表管理服务已经接受到任务，实际驱动需要业务关注自己配置的执行结果的管道
     *
     * @param agentOperateDtoList 批量任务
     * @return 批量任务存储结果
     * @throws TaskSaveException 任务存储异常
     */
    @Override
    @Async
    public List<AgentOperateResultDto> send(List<AgentOperateDto> agentOperateDtoList) {
        agentOperateDtoList.forEach(dto -> {
            log.info("mock receive task,bizId is {}", dto.getBizId());
            var res = new SendRes(
                dto.getBizId(),
                dto.getAgentOptionDto().getAgentHost(),
                dto.getAgentOptionDto().getAgentPort().intValue(),
                SnowflakeIdWorker.generateId(),
                "ok"
            );
            try {
                //发送结果队列
                iPublisher.apply("scriptSendResult-out-0", res);
            } catch (CommunicationException e) {
                log.error("mock推送send-result失败,{}", dto, e);
                return;
            }

            //执行结果队列
            try {
                var execDtoList = new ArrayList<String>();
                var stdout = cn.hutool.core.codec.Base64.encode("mock Agent Stdout2131232131" + StrPool.LF + "0");
                var stderr = cn.hutool.core.codec.Base64.encode(StrPool.LF);
                var execDto = new ExecRes(
                    StrUtil.subAfter(dto.getBizId(), StrPool.DASHED, true),
                    stdout,
                    stderr,
                    stdout,
                    1,
                    20
                );
                execDtoList.add(objectMapper.writeValueAsString(execDto));
                iPublisher.apply("scriptExecuteResult-out-0", execDtoList);
            } catch (CommunicationException | JsonProcessingException e) {
                log.error("mock推送execute-result失败,{}", dto, e);
                return;
            }
            log.info("mock推送execute-result成功,bizId:{}", dto.getBizId());
        });
        return null;
    }

    /**
     * 任务发送
     *
     * @param agentOperateDTO 任务信息
     * @return 发送结果c
     * @throws TaskSaveException 任务存储异常
     */
    @Override
    public AgentSyncOperateResultDto sendSync(AgentOperateDto agentOperateDTO) throws SendException {
        return null;
    }

    /**
     * 获取输出详情接口实现方法
     *
     * @param bizKey 业务核心标记支持模糊匹配
     * @return 输出结果
     */
    @Override
    public List<AgentTaskResultDto> get(String bizKey) {
       
        return new ArrayList<>();
    }

    @Override
    public List<AgentTaskResultDto> list(List<String> bizIdList) {
        return new ArrayList<>();
    }

    @Override
    public PageInfo<AgentTaskDto> list(AgentTaskQueryDto agentTaskQueryDto) {
        return new PageInfo<>();
    }

    @Override
    public AgentTaskInputOutputDto getInputOutPut(Long taskId) {
        return new AgentTaskInputOutputDto();
    }
}
