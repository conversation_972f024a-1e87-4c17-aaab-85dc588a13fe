package com.ideal.mock.ag.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurerSupport;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

@EnableAsync
@Configuration
@Slf4j
public class Config extends AsyncConfigurerSupport {
    @Value("${corePoolSize:30}")
    private int corePoolSize;

    @Value("${maxPoolSize:300}")
    private int maxPoolSize;

    @Value("${queueCapacity:10000}")
    private int queueCapacity;

    @Override
    public Executor getAsyncExecutor() {
        var executor = new ThreadPoolTaskExecutor();
        // 核心线程池大小
        executor.setCorePoolSize(corePoolSize); // 30
        // 最大线程数
        executor.setMaxPoolSize(maxPoolSize); // 1000
        // 队列容量
        executor.setQueueCapacity(queueCapacity); // 5000
        // 空闲活跃时间
        executor.setKeepAliveSeconds(300);
        // 优雅停机，等待线程池任务都执行完毕再销毁
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 线程池销毁等待时间
        executor.setAwaitTerminationSeconds(60);
        // 线程名字前缀
        executor.setThreadNamePrefix("mock-async-");
        var rejectionPolicy = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(rejectionPolicy);
        executor.initialize();
        return executor;
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> log.error("catch exception by spring executing async method '{}'", method, ex);
    }

    /**
     * 虚拟线程执行器配置
     * 当app.virtual-threads.enabled=true时启用
     * 虚拟线程适合I/O密集型任务，可以大大减少线程创建和切换的开销
     */
    @Bean("virtualThreadExecutor")
    @ConditionalOnProperty(name = "app.virtual-threads.enabled", havingValue = "true")
    public Executor virtualThreadExecutor() {
        log.info("启用虚拟线程执行器");
        return Executors.newVirtualThreadPerTaskExecutor();
    }

}
