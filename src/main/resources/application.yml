spring:
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  profiles:
    active: aptzyh
    default: aptzyh
  application:
    name: mock-agent-gateway
  cloud:
    stream:
      rocketmq:
        binder:
          #rocketmq nameServer地址
          name-server: *************:9876;*************:9876
      bindings:
        scriptExecuteResult-out-0:
          destination: script-execute-result-aptzyh
          content-type: application/json
          group: script-execute-result-group-aptzyh
        # scriptErrorResult-out-0:
        #   destination: script-error-result-aptzyh
        #   content-type: application/json
        #   group: script-error-result-group-aptzyh
        scriptSendResult-out-0:
          destination: script-send-result-aptzyh
          content-type: application/json
          group: script-send-result-group-aptzyh
dubbo:
  application:
    name: mock-agent-gateway-dubbo
    #dubbo qos服务端口
    qosPort: 12270
  registry:
    address: nacos://************:8848?namespace=123456
  protocol:
    #dubbo服务端口
    port: 22270
    #dubbo线程池大小
    threads: 300
  provider:
    timeout: 200000
  consumer:
    timeout: 200000
    retries: 5
    check: false