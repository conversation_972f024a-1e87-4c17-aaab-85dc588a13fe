2025-07-31 10:38:34.694 [TID: N/A] [main] INFO  o.a.d.s.b.c.event.WelcomeLogoApplicationListener - [DUBBO] 

 :: Dubbo (v3.2.5) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>
, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:34.784 [TID: N/A] [background-preinit] INFO  org.hibernate.validator.internal.util.Version -HV000001: Hibernate Validator 6.2.5.Final
2025-07-31 10:38:34.808 [TID: N/A] [main] INFO  c.i.m.MockAgentGatewayApplicationTests -Starting MockAgentGatewayApplicationTests using Java 21.0.8 on PC-ZZZ with PID 77548 (started by root in /root/entegor_v9_work/mock-agent-gateway)
2025-07-31 10:38:34.808 [TID: N/A] [main] DEBUG c.i.m.MockAgentGatewayApplicationTests -Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 10:38:34.809 [TID: N/A] [main] INFO  c.i.m.MockAgentGatewayApplicationTests -The following 1 profile is active: "aptzyh"
2025-07-31 10:38:35.175 [TID: N/A] [main] INFO  org.apache.dubbo.rpc.model.FrameworkModel - [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.191 [TID: N/A] [main] INFO  o.a.d.common.resource.GlobalResourcesRepository - [DUBBO] Creating global shared handler ..., dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.281 [TID: N/A] [main] INFO  org.apache.dubbo.rpc.model.ApplicationModel - [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.284 [TID: N/A] [main] INFO  org.apache.dubbo.rpc.model.ScopeModel - [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.329 [TID: N/A] [main] INFO  o.a.dubbo.config.context.AbstractConfigManager - [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.330 [TID: N/A] [main] INFO  o.a.dubbo.config.context.AbstractConfigManager - [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.352 [TID: N/A] [main] INFO  o.a.dubbo.common.utils.SerializeSecurityManager - [DUBBO] Serialize check serializable: true, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.354 [TID: N/A] [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator - [DUBBO] Read serialize allow list from jar:file:/root/.m2/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.allowlist, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.387 [TID: N/A] [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator - [DUBBO] Read serialize blocked list from jar:file:/root/.m2/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.blockedlist, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.594 [TID: N/A] [main] INFO  org.apache.dubbo.rpc.model.ApplicationModel - [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.594 [TID: N/A] [main] INFO  org.apache.dubbo.rpc.model.ScopeModel - [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.600 [TID: N/A] [main] INFO  o.a.dubbo.config.context.AbstractConfigManager - [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.600 [TID: N/A] [main] INFO  o.a.dubbo.config.context.AbstractConfigManager - [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.603 [TID: N/A] [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator - [DUBBO] Read serialize allow list from jar:file:/root/.m2/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.allowlist, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.603 [TID: N/A] [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator - [DUBBO] Read serialize blocked list from jar:file:/root/.m2/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.blockedlist, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.611 [TID: N/A] [main] INFO  o.a.d.config.spring.context.DubboSpringInitializer - [DUBBO] Use default application: Dubbo Application[1.1](unknown), dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.611 [TID: N/A] [main] INFO  org.apache.dubbo.rpc.model.ScopeModel - [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.613 [TID: N/A] [main] INFO  o.a.dubbo.config.context.AbstractConfigManager - [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.615 [TID: N/A] [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator - [DUBBO] Read serialize allow list from jar:file:/root/.m2/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.allowlist, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.616 [TID: N/A] [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator - [DUBBO] Read serialize blocked list from jar:file:/root/.m2/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.blockedlist, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.617 [TID: N/A] [main] INFO  o.a.d.config.spring.context.DubboSpringInitializer - [DUBBO] Use default module model of target application: Dubbo Module[1.1.1], dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.617 [TID: N/A] [main] INFO  o.a.d.config.spring.context.DubboSpringInitializer - [DUBBO] Bind Dubbo Module[1.1.1] to spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@71262020, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.863 [TID: N/A] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.864 [TID: N/A] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.866 [TID: N/A] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - [DUBBO] Found 1 classes annotated by Dubbo @Service under package [com.ideal.mock.ag]: [com.ideal.mock.ag.service.DubboAgentOperateApiImpl], dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.879 [TID: N/A] [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - [DUBBO] Register ServiceBean[ServiceBean:com.ideal.agent.gateway.api.AgentOperateApi:1.0.0:aptzyh.agent-gateway]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.5, current host: ************
2025-07-31 10:38:35.885 [TID: N/A] [main] INFO  o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor -No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-31 10:38:35.905 [TID: N/A] [main] INFO  o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor -No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
